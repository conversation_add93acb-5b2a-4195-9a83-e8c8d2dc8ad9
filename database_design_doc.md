# 事绪录小程序数据库设计文档

## 概述

本文档描述了"事绪录"微信小程序的数据库表结构设计，该小程序是一个基于微信生态的轻量级协作任务管理系统，核心特性包括"标签即群组"和"分享即加入"。

## 核心设计理念

1. **标签即群组**：每个标签不仅是任务分类，更是一个动态的协作空间
2. **分享即加入**：通过分享任务实现无摩擦的团队组建
3. **微信生态集成**：深度集成微信登录、通知等功能
4. **轻量化设计**：避免过度复杂的功能，专注核心协作场景

## 数据库表结构

### 1. 用户表 (users)

存储微信用户的基本信息。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) unsigned | 用户ID | 主键，自增 |
| openid | varchar(64) | 微信openid | 唯一，非空 |
| nickname | varchar(100) | 微信昵称 | 可空 |
| avatar_url | varchar(500) | 头像URL | 可空 |
| phone | varchar(20) | 手机号 | 可空 |
| email | varchar(100) | 邮箱 | 可空 |
| is_active | tinyint(1) | 是否激活 | 默认1 |
| last_login_at | timestamp | 最后登录时间 | 可空 |
| created_at | timestamp | 创建时间 | 默认当前时间 |
| updated_at | timestamp | 更新时间 | 自动更新 |

**索引**：
- 主键：id
- 唯一索引：openid
- 普通索引：nickname, created_at

### 2. 标签表 (tags)

实现"标签即群组"概念的核心表。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) unsigned | 标签ID | 主键，自增 |
| name | varchar(100) | 标签名称 | 非空 |
| color | varchar(20) | 标签颜色 | 默认#007AFF |
| description | text | 标签描述 | 可空 |
| creator_id | bigint(20) unsigned | 创建者ID | 外键，非空 |
| is_active | tinyint(1) | 是否激活 | 默认1 |
| member_count | int(11) | 成员数量 | 冗余字段，默认1 |
| task_count | int(11) | 任务数量 | 冗余字段，默认0 |
| created_at | timestamp | 创建时间 | 默认当前时间 |
| updated_at | timestamp | 更新时间 | 自动更新 |

**外键约束**：
- creator_id → users.id (CASCADE DELETE)

### 3. 标签成员关系表 (tag_members)

管理用户与标签的关系及权限。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) unsigned | 关系ID | 主键，自增 |
| tag_id | bigint(20) unsigned | 标签ID | 外键，非空 |
| user_id | bigint(20) unsigned | 用户ID | 外键，非空 |
| role | enum | 角色 | owner/admin/member |
| is_active | tinyint(1) | 是否激活 | 默认1 |
| joined_at | timestamp | 加入时间 | 默认当前时间 |
| created_at | timestamp | 创建时间 | 默认当前时间 |
| updated_at | timestamp | 更新时间 | 自动更新 |

**权限说明**：
- **owner**：标签创建者，拥有所有权限
- **admin**：管理员，可以邀请成员、管理任务
- **member**：普通成员，可以查看和编辑分配给自己的任务

**约束**：
- 唯一复合索引：(tag_id, user_id)
- 外键约束：tag_id → tags.id, user_id → users.id

### 4. 任务表 (tasks)

存储任务的详细信息。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) unsigned | 任务ID | 主键，自增 |
| title | varchar(255) | 任务标题 | 非空 |
| description | text | 任务描述 | 可空 |
| tag_id | bigint(20) unsigned | 所属标签ID | 外键，非空 |
| creator_id | bigint(20) unsigned | 创建者ID | 外键，非空 |
| assignee_id | bigint(20) unsigned | 执行人ID | 外键，可空 |
| status | enum | 任务状态 | pending/in_progress/completed/cancelled |
| priority | enum | 优先级 | low/medium/high |
| due_date | timestamp | 截止日期 | 可空 |
| completed_at | timestamp | 完成时间 | 可空 |
| is_archived | tinyint(1) | 是否归档 | 默认0 |
| comment_count | int(11) | 评论数量 | 冗余字段，默认0 |
| created_at | timestamp | 创建时间 | 默认当前时间 |
| updated_at | timestamp | 更新时间 | 自动更新 |

**状态流转**：
- pending → in_progress → completed
- 任何状态 → cancelled

### 5. 任务评论表 (task_comments)

支持任务讨论功能。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) unsigned | 评论ID | 主键，自增 |
| task_id | bigint(20) unsigned | 任务ID | 外键，非空 |
| user_id | bigint(20) unsigned | 评论者ID | 外键，非空 |
| content | text | 评论内容 | 非空 |
| parent_id | bigint(20) unsigned | 父评论ID | 外键，可空 |
| is_deleted | tinyint(1) | 是否删除 | 默认0 |
| created_at | timestamp | 创建时间 | 默认当前时间 |
| updated_at | timestamp | 更新时间 | 自动更新 |

**特性**：
- 支持评论回复（通过parent_id）
- 软删除机制

### 6. 分享令牌表 (share_tokens)

实现"分享即加入"功能的核心表。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) unsigned | 令牌ID | 主键，自增 |
| token | varchar(128) | 分享令牌 | 唯一，非空 |
| task_id | bigint(20) unsigned | 关联任务ID | 外键，非空 |
| tag_id | bigint(20) unsigned | 关联标签ID | 外键，非空 |
| creator_id | bigint(20) unsigned | 创建者ID | 外键，非空 |
| expires_at | timestamp | 过期时间 | 非空 |
| used_at | timestamp | 使用时间 | 可空 |
| used_by | bigint(20) unsigned | 使用者ID | 外键，可空 |
| is_active | tinyint(1) | 是否有效 | 默认1 |
| max_uses | int(11) | 最大使用次数 | 默认1 |
| use_count | int(11) | 已使用次数 | 默认0 |
| created_at | timestamp | 创建时间 | 默认当前时间 |

**安全机制**：
- 令牌唯一性
- 过期时间控制
- 使用次数限制
- 一次性使用（默认）

### 7. 通知记录表 (notifications)

管理系统通知和微信消息推送。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) unsigned | 通知ID | 主键，自增 |
| user_id | bigint(20) unsigned | 接收用户ID | 外键，非空 |
| type | enum | 通知类型 | 见下方说明 |
| title | varchar(255) | 通知标题 | 非空 |
| content | text | 通知内容 | 可空 |
| related_task_id | bigint(20) unsigned | 相关任务ID | 外键，可空 |
| related_tag_id | bigint(20) unsigned | 相关标签ID | 外键，可空 |
| related_user_id | bigint(20) unsigned | 相关用户ID | 外键，可空 |
| is_read | tinyint(1) | 是否已读 | 默认0 |
| sent_at | timestamp | 发送时间 | 可空 |
| wx_template_id | varchar(100) | 微信模板ID | 可空 |
| wx_form_id | varchar(100) | 微信表单ID | 可空 |
| created_at | timestamp | 创建时间 | 默认当前时间 |

**通知类型**：
- task_assigned：任务分配
- task_updated：任务更新
- task_commented：任务评论
- task_due：任务到期提醒
- task_completed：任务完成
- tag_invited：标签邀请

## 视图设计

### 1. 用户任务统计视图 (user_task_stats)

提供用户维度的任务统计信息，用于首页概览显示。

### 2. 标签统计视图 (tag_stats)

提供标签维度的统计信息，包括成员数量、任务数量等。

## 索引策略

1. **主键索引**：所有表都有自增主键
2. **唯一索引**：openid、token等唯一字段
3. **外键索引**：所有外键字段都建立索引
4. **复合索引**：针对常用查询组合建立复合索引
5. **业务索引**：status、priority、due_date等业务字段

## 数据完整性

1. **外键约束**：确保数据引用完整性
2. **级联删除**：合理设置级联删除规则
3. **枚举约束**：使用枚举类型限制字段值
4. **非空约束**：关键字段设置非空约束

## 性能优化

1. **冗余字段**：适当使用冗余字段减少关联查询
2. **分页查询**：大数据量查询使用分页
3. **索引优化**：根据查询模式优化索引
4. **视图使用**：复杂统计查询使用视图

## 扩展性考虑

1. **软删除**：重要数据使用软删除机制
2. **审计日志**：提供操作日志表用于审计
3. **配置管理**：系统配置表支持动态配置
4. **版本控制**：预留版本字段用于乐观锁

## 安全性

1. **权限控制**：基于角色的权限管理
2. **令牌安全**：分享令牌的安全机制
3. **数据加密**：敏感数据可考虑加密存储
4. **访问控制**：数据库访问权限控制
