<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分享邀请 - 事绪录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        
        .nav-bar {
            height: 60px;
            background: #ffffff;
            border-bottom: 1px solid #e5e5e5;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        
        .content-area {
            background: #F2F2F7;
            min-height: calc(100vh - 104px);
            padding: 16px;
        }
        
        .share-preview {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .share-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 16px;
            position: relative;
            overflow: hidden;
        }
        
        .share-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></svg>') no-repeat;
            background-size: 80px 80px;
        }
        
        .share-content {
            position: relative;
            z-index: 1;
        }
        
        .share-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }
        
        .app-icon {
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #667eea;
        }
        
        .app-info h3 {
            font-weight: bold;
            font-size: 16px;
        }
        
        .app-info p {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .task-preview {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
        }
        
        .task-title {
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .task-meta {
            font-size: 12px;
            opacity: 0.8;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .invite-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .inviter {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .inviter-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px solid white;
        }
        
        .join-badge {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .share-options {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .option-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        
        .option-item {
            background: #F8F9FA;
            border: 2px solid transparent;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .option-item.selected {
            border-color: #007AFF;
            background: #E3F2FD;
        }
        
        .option-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            margin: 0 auto 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .option-title {
            font-weight: 600;
            color: #1D1D1F;
            margin-bottom: 4px;
        }
        
        .option-desc {
            font-size: 12px;
            color: #6B7280;
        }
        
        .settings-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #F2F2F7;
        }
        
        .setting-item:last-child {
            border-bottom: none;
        }
        
        .setting-label {
            flex: 1;
        }
        
        .setting-title {
            font-weight: 500;
            color: #1D1D1F;
            margin-bottom: 2px;
        }
        
        .setting-desc {
            font-size: 12px;
            color: #6B7280;
        }
        
        .switch-toggle {
            position: relative;
            width: 48px;
            height: 28px;
            background: #E5E5EA;
            border-radius: 14px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .switch-toggle.active {
            background: #007AFF;
        }
        
        .switch-toggle::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .switch-toggle.active::after {
            transform: translateX(20px);
        }
        
        .permission-select {
            padding: 8px 12px;
            border: 1px solid #D1D1D6;
            border-radius: 8px;
            background: white;
            font-size: 14px;
            cursor: pointer;
        }
        
        .share-button {
            width: 100%;
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .share-button:hover {
            background: #0056CC;
        }
        
        .share-button:disabled {
            background: #D1D1D6;
            cursor: not-allowed;
        }
        
        .tips-card {
            background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%);
            border: 1px solid #FFB74D;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
        }
        
        .tips-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }
        
        .tips-icon {
            width: 24px;
            height: 24px;
            background: #FF9800;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .tips-title {
            font-weight: 600;
            color: #E65100;
        }
        
        .tips-content {
            font-size: 14px;
            color: #BF360C;
            line-height: 1.4;
        }
        
        .link-preview {
            background: #F8F9FA;
            border: 1px dashed #D1D1D6;
            border-radius: 8px;
            padding: 12px;
            font-family: monospace;
            font-size: 12px;
            color: #6B7280;
            word-break: break-all;
            margin-top: 8px;
        }
        
        .copy-button {
            background: #F2F2F7;
            color: #007AFF;
            border: 1px solid #D1D1D6;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .copy-button:hover {
            background: #E5E5EA;
        }
        
        .success-message {
            background: #E8F5E8;
            color: #2E7D32;
            border: 1px solid #4CAF50;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            font-size: 14px;
            margin-top: 12px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <span>9:41</span>
        <div class="flex items-center gap-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <button class="flex items-center gap-2 text-blue-600">
            <i class="fas fa-chevron-left"></i>
            <span>返回</span>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">分享邀请</h1>
        <div></div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
        <!-- 分享预览 -->
        <div class="share-preview">
            <h3 class="font-semibold text-gray-900 mb-3">微信分享预览</h3>
            <div class="share-card">
                <div class="share-content">
                    <div class="share-header">
                        <div class="app-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="app-info">
                            <h3>事绪录</h3>
                            <p>言语成绪，录而事成</p>
                        </div>
                    </div>
                    
                    <div class="task-preview">
                        <div class="task-title">完成品牌Logo最终设计稿</div>
                        <div class="task-meta">
                            <span># 项目Alpha</span>
                            <span>•</span>
                            <span>截止今天18:00</span>
                        </div>
                    </div>
                    
                    <div class="invite-info">
                        <div class="inviter">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=24&h=24&fit=crop&crop=face&auto=format" 
                                 class="inviter-avatar" alt="安娜">
                            <span class="text-sm">安娜 邀请您协作</span>
                        </div>
                        <div class="join-badge">点击加入</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分享方式选择 -->
        <div class="share-options">
            <h3 class="font-semibold text-gray-900 mb-4">选择分享方式</h3>
            <div class="option-grid">
                <div class="option-item selected">
                    <div class="option-icon bg-green-100 text-green-600">
                        <i class="fab fa-weixin"></i>
                    </div>
                    <div class="option-title">微信好友</div>
                    <div class="option-desc">发送给个人好友</div>
                </div>
                
                <div class="option-item">
                    <div class="option-icon bg-green-100 text-green-600">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="option-title">微信群聊</div>
                    <div class="option-desc">分享到群聊</div>
                </div>
                
                <div class="option-item">
                    <div class="option-icon bg-blue-100 text-blue-600">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="option-title">复制链接</div>
                    <div class="option-desc">生成邀请链接</div>
                </div>
                
                <div class="option-item">
                    <div class="option-icon bg-purple-100 text-purple-600">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="option-title">二维码</div>
                    <div class="option-desc">生成二维码分享</div>
                </div>
            </div>
        </div>

        <!-- 权限设置 -->
        <div class="settings-card">
            <h3 class="font-semibold text-gray-900 mb-4">邀请设置</h3>
            
            <div class="setting-item">
                <div class="setting-label">
                    <div class="setting-title">访问权限</div>
                    <div class="setting-desc">设置受邀者的标签群组权限</div>
                </div>
                <select class="permission-select">
                    <option>成员权限</option>
                    <option>管理员权限</option>
                </select>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <div class="setting-title">自动通知</div>
                    <div class="setting-desc">当有人通过此链接加入时通知我</div>
                </div>
                <div class="switch-toggle active"></div>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <div class="setting-title">限制使用次数</div>
                    <div class="setting-desc">链接只能被使用一次</div>
                </div>
                <div class="switch-toggle"></div>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <div class="setting-title">有效期限制</div>
                    <div class="setting-desc">7天后链接自动失效</div>
                </div>
                <div class="switch-toggle active"></div>
            </div>
        </div>

        <!-- 使用提示 -->
        <div class="tips-card">
            <div class="tips-header">
                <div class="tips-icon">
                    <i class="fas fa-lightbulb"></i>
                </div>
                <div class="tips-title">分享即加入机制</div>
            </div>
            <div class="tips-content">
                通过微信分享任务卡片，接收者点击后即可自动获得对应标签群组的访问权限，
                无需繁琐的注册或审批流程。这是事绪录的核心创新功能。
            </div>
        </div>

        <!-- 链接预览 -->
        <div class="share-options" id="linkPreview" style="display: none;">
            <h3 class="font-semibold text-gray-900 mb-3">邀请链接</h3>
            <div class="flex items-center gap-2">
                <div class="link-preview flex-1">
                    https://mp.weixin.qq.com/shixulu?task=abc123&token=xyz789&invite=true
                </div>
                <button class="copy-button">
                    <i class="fas fa-copy mr-1"></i>
                    复制
                </button>
            </div>
            <div class="success-message" id="copySuccess" style="display: none;">
                <i class="fas fa-check mr-2"></i>
                链接已复制到剪贴板
            </div>
        </div>

        <!-- 分享按钮 -->
        <button class="share-button" id="shareButton">
            <i class="fab fa-weixin"></i>
            分享到微信
        </button>

        <!-- 历史邀请 -->
        <div class="settings-card">
            <h3 class="font-semibold text-gray-900 mb-4">最近邀请</h3>
            
            <div class="space-y-3">
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center gap-3">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b2a1?w=32&h=32&fit=crop&crop=face&auto=format" 
                             class="w-8 h-8 rounded-full" alt="张设计师">
                        <div>
                            <div class="font-medium text-gray-900">张设计师</div>
                            <div class="text-xs text-gray-500">2小时前加入 # 项目Alpha</div>
                        </div>
                    </div>
                    <span class="text-xs text-green-600 font-medium">已加入</span>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center gap-3">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face&auto=format" 
                             class="w-8 h-8 rounded-full" alt="李工程师">
                        <div>
                            <div class="font-medium text-gray-900">李工程师</div>
                            <div class="text-xs text-gray-500">昨天邀请至 # 客户Beta</div>
                        </div>
                    </div>
                    <span class="text-xs text-orange-600 font-medium">邀请中</span>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center gap-3">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face&auto=format" 
                             class="w-8 h-8 rounded-full" alt="王产品">
                        <div>
                            <div class="font-medium text-gray-900">王产品</div>
                            <div class="text-xs text-gray-500">3天前加入 # 紧急处理</div>
                        </div>
                    </div>
                    <span class="text-xs text-green-600 font-medium">已加入</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 分享方式选择
        document.querySelectorAll('.option-item').forEach(item => {
            item.addEventListener('click', () => {
                document.querySelectorAll('.option-item').forEach(i => i.classList.remove('selected'));
                item.classList.add('selected');
                
                const title = item.querySelector('.option-title').textContent;
                const shareButton = document.getElementById('shareButton');
                const linkPreview = document.getElementById('linkPreview');
                
                if (title === '复制链接') {
                    shareButton.innerHTML = '<i class="fas fa-link"></i> 生成邀请链接';
                    linkPreview.style.display = 'block';
                } else if (title === '二维码') {
                    shareButton.innerHTML = '<i class="fas fa-qrcode"></i> 生成二维码';
                    linkPreview.style.display = 'none';
                } else if (title === '微信群聊') {
                    shareButton.innerHTML = '<i class="fas fa-users"></i> 分享到群聊';
                    linkPreview.style.display = 'none';
                } else {
                    shareButton.innerHTML = '<i class="fab fa-weixin"></i> 分享到微信';
                    linkPreview.style.display = 'none';
                }
            });
        });

        // 开关切换
        document.querySelectorAll('.switch-toggle').forEach(toggle => {
            toggle.addEventListener('click', () => {
                toggle.classList.toggle('active');
            });
        });

        // 复制链接
        document.querySelector('.copy-button').addEventListener('click', () => {
            const link = document.querySelector('.link-preview').textContent;
            navigator.clipboard.writeText(link).then(() => {
                const success = document.getElementById('copySuccess');
                success.style.display = 'block';
                setTimeout(() => {
                    success.style.display = 'none';
                }, 2000);
            });
        });

        // 分享按钮点击
        document.getElementById('shareButton').addEventListener('click', () => {
            const selectedOption = document.querySelector('.option-item.selected .option-title').textContent;
            
            if (selectedOption === '复制链接') {
                document.querySelector('.copy-button').click();
            } else {
                // 模拟微信分享
                alert(`正在打开微信分享界面...`);
            }
        });

        // 权限选择变更
        document.querySelector('.permission-select').addEventListener('change', function() {
            console.log('权限设置为:', this.value);
        });
    </script>
</body>
</html>
