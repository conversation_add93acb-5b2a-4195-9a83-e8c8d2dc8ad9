<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>事绪录 - 高保真原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* iPhone 15 Pro 尺寸适配 */
        .phone-frame {
            width: 393px;
            height: 852px;
            border-radius: 47px;
            box-shadow: 0 0 50px rgba(0,0,0,0.3);
            overflow: hidden;
            position: relative;
            background: #000;
            padding: 8px;
        }
        
        .phone-screen {
            width: 100%;
            height: 100%;
            border-radius: 39px;
            overflow: hidden;
            background: #fff;
        }
        
        .prototype-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 40px;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .screen-title {
            position: absolute;
            top: -30px;
            left: 0;
            right: 0;
            text-align: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        
        /* iOS 状态栏样式 */
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        
        /* 导航栏样式 */
        .nav-bar {
            height: 60px;
            background: #ffffff;
            border-bottom: 1px solid #e5e5e5;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        /* 底部 Tab Bar */
        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: #ffffff;
            border-top: 1px solid #e5e5e5;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding-bottom: 20px;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .tab-item.active {
            color: #007AFF;
        }
        
        .tab-item i {
            font-size: 20px;
        }
        
        .tab-item span {
            font-size: 10px;
            font-weight: 500;
        }
        
        /* 内容区域 */
        .content-area {
            padding-top: 104px; /* 状态栏 + 导航栏 */
            padding-bottom: 83px; /* Tab Bar */
            height: 100%;
            overflow-y: auto;
        }
        
        /* 任务卡片样式 */
        .task-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin: 8px 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #007AFF;
        }
        
        .tag-pill {
            display: inline-block;
            background: #E3F2FD;
            color: #1976D2;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }
        
        /* 浮动按钮 */
        .fab {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #007AFF;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0,122,255,0.4);
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .fab:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <!-- 首页 - 任务列表 -->
        <div class="phone-frame" style="position: relative;">
            <div class="screen-title">首页 - 任务列表</div>
            <div class="phone-screen">
                <iframe src="home.html" width="100%" height="100%" frameborder="0"></iframe>
            </div>
        </div>

        <!-- 任务详情页 -->
        <div class="phone-frame" style="position: relative;">
            <div class="screen-title">任务详情页</div>
            <div class="phone-screen">
                <iframe src="task-detail.html" width="100%" height="100%" frameborder="0"></iframe>
            </div>
        </div>

        <!-- 创建任务页 -->
        <div class="phone-frame" style="position: relative;">
            <div class="screen-title">创建任务</div>
            <div class="phone-screen">
                <iframe src="create-task.html" width="100%" height="100%" frameborder="0"></iframe>
            </div>
        </div>

        <!-- 标签管理页 -->
        <div class="phone-frame" style="position: relative;">
            <div class="screen-title">标签管理</div>
            <div class="phone-screen">
                <iframe src="tags.html" width="100%" height="100%" frameborder="0"></iframe>
            </div>
        </div>

        <!-- 个人中心 -->
        <div class="phone-frame" style="position: relative;">
            <div class="screen-title">个人中心</div>
            <div class="phone-screen">
                <iframe src="profile.html" width="100%" height="100%" frameborder="0"></iframe>
            </div>
        </div>

        <!-- 分享邀请页 -->
        <div class="phone-frame" style="position: relative;">
            <div class="screen-title">分享邀请</div>
            <div class="phone-screen">
                <iframe src="share-invite.html" width="100%" height="100%" frameborder="0"></iframe>
            </div>
        </div>
    </div>
</body>
</html>
