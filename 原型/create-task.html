<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建任务 - 事绪录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        
        .nav-bar {
            height: 60px;
            background: #ffffff;
            border-bottom: 1px solid #e5e5e5;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        
        .content-area {
            background: #F2F2F7;
            min-height: calc(100vh - 104px);
            padding: 16px;
        }
        
        .form-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            margin-bottom: 16px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: #1D1D1F;
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #D1D1D6;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.2s;
            background: #FAFAFA;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #007AFF;
            background: white;
        }
        
        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .priority-buttons {
            display: flex;
            gap: 12px;
        }
        
        .priority-btn {
            flex: 1;
            padding: 12px;
            border: 1px solid #D1D1D6;
            border-radius: 10px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
            font-weight: 500;
        }
        
        .priority-btn.active {
            border-color: #007AFF;
            background: #E3F2FD;
            color: #007AFF;
        }
        
        .priority-btn.high.active {
            border-color: #FF3B30;
            background: #FFEBEA;
            color: #FF3B30;
        }
        
        .priority-btn.medium.active {
            border-color: #FF9500;
            background: #FFF3E0;
            color: #FF9500;
        }
        
        .priority-btn.low.active {
            border-color: #34C759;
            background: #E8F5E8;
            color: #34C759;
        }
        
        .tag-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 12px;
        }
        
        .tag-option {
            padding: 8px 16px;
            background: #F2F2F7;
            border: 1px solid #D1D1D6;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .tag-option.selected {
            background: #007AFF;
            color: white;
            border-color: #007AFF;
        }
        
        .tag-input {
            padding: 8px 16px;
            border: 1px dashed #007AFF;
            border-radius: 20px;
            font-size: 14px;
            background: transparent;
            color: #007AFF;
            min-width: 120px;
        }
        
        .tag-input:focus {
            outline: none;
            border-style: solid;
        }
        
        .date-time-input {
            position: relative;
        }
        
        .create-btn {
            width: 100%;
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .create-btn:hover {
            background: #0056CC;
        }
        
        .create-btn:disabled {
            background: #D1D1D6;
            cursor: not-allowed;
        }
        
        .member-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }
        
        .member-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border: 1px solid #D1D1D6;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
        }
        
        .member-option.selected {
            border-color: #007AFF;
            background: #E3F2FD;
        }
        
        .member-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .quick-actions {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .quick-action {
            flex: 1;
            padding: 12px;
            background: #F2F2F7;
            border: none;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: background 0.2s;
            font-size: 14px;
        }
        
        .quick-action:hover {
            background: #E5E5EA;
        }
        
        .quick-action.active {
            background: #007AFF;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <span>9:41</span>
        <div class="flex items-center gap-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <button class="flex items-center gap-2 text-blue-600">
            <i class="fas fa-times text-lg"></i>
            <span>取消</span>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">创建任务</h1>
        <button class="text-blue-600 font-semibold">保存</button>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
        <!-- 快速操作 -->
        <div class="form-card">
            <h3 class="font-semibold text-gray-900 mb-3">快速创建</h3>
            <div class="quick-actions">
                <button class="quick-action active">
                    <i class="fas fa-clipboard-list mb-1"></i>
                    <div>普通任务</div>
                </button>
                <button class="quick-action">
                    <i class="fas fa-clock mb-1"></i>
                    <div>定时提醒</div>
                </button>
                <button class="quick-action">
                    <i class="fas fa-sync mb-1"></i>
                    <div>重复任务</div>
                </button>
            </div>
        </div>

        <!-- 基本信息 -->
        <div class="form-card">
            <div class="form-group">
                <label class="form-label">任务标题 *</label>
                <input type="text" class="form-input" placeholder="输入任务标题..." value="">
            </div>

            <div class="form-group">
                <label class="form-label">详细描述</label>
                <textarea class="form-input form-textarea" placeholder="详细描述任务内容、要求和注意事项..."></textarea>
            </div>
        </div>

        <!-- 优先级 -->
        <div class="form-card">
            <div class="form-group">
                <label class="form-label">优先级</label>
                <div class="priority-buttons">
                    <button class="priority-btn high">
                        <i class="fas fa-exclamation-triangle text-red-500 mb-1"></i>
                        <div>高</div>
                    </button>
                    <button class="priority-btn medium active">
                        <i class="fas fa-minus-circle text-orange-500 mb-1"></i>
                        <div>中</div>
                    </button>
                    <button class="priority-btn low">
                        <i class="fas fa-arrow-down text-green-500 mb-1"></i>
                        <div>低</div>
                    </button>
                </div>
            </div>
        </div>

        <!-- 标签选择 -->
        <div class="form-card">
            <div class="form-group">
                <label class="form-label">选择标签</label>
                <div class="tag-selector">
                    <button class="tag-option"># 项目Alpha</button>
                    <button class="tag-option selected"># 客户Beta</button>
                    <button class="tag-option"># 个人事务</button>
                    <button class="tag-option"># 紧急处理</button>
                    <input type="text" class="tag-input" placeholder="新建标签...">
                </div>
                <p class="text-sm text-gray-500 mt-2">选择标签可以将任务归类到对应的协作空间</p>
            </div>
        </div>

        <!-- 执行人 -->
        <div class="form-card">
            <div class="form-group">
                <label class="form-label">指派给</label>
                <div class="member-selector">
                    <div class="member-option selected">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=24&h=24&fit=crop&crop=face&auto=format" 
                             class="member-avatar" alt="我">
                        <span>我</span>
                        <i class="fas fa-check text-blue-600 text-sm"></i>
                    </div>
                    <div class="member-option">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b2a1?w=24&h=24&fit=crop&crop=face&auto=format" 
                             class="member-avatar" alt="张设计师">
                        <span>张设计师</span>
                    </div>
                    <div class="member-option">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=24&h=24&fit=crop&crop=face&auto=format" 
                             class="member-avatar" alt="李工程师">
                        <span>李工程师</span>
                    </div>
                    <div class="member-option">
                        <i class="fas fa-plus text-blue-600"></i>
                        <span class="text-blue-600">邀请成员</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 时间设置 -->
        <div class="form-card">
            <div class="form-group">
                <label class="form-label">截止时间</label>
                <div class="date-time-input">
                    <input type="datetime-local" class="form-input" value="2024-01-15T18:00">
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">提醒时间</label>
                <select class="form-input">
                    <option>不提醒</option>
                    <option selected>提前1小时</option>
                    <option>提前2小时</option>
                    <option>提前1天</option>
                    <option>自定义</option>
                </select>
            </div>
        </div>

        <!-- 附件 -->
        <div class="form-card">
            <div class="form-group">
                <label class="form-label">附件</label>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <i class="fas fa-cloud-upload-alt text-gray-400 text-2xl mb-2"></i>
                    <p class="text-gray-500">点击上传文件或图片</p>
                    <p class="text-xs text-gray-400 mt-1">支持 PDF、图片、Word 等格式</p>
                </div>
            </div>
        </div>

        <!-- 协作设置 -->
        <div class="form-card">
            <div class="form-group">
                <label class="form-label">协作设置</label>
                <div class="space-y-3">
                    <label class="flex items-center gap-3">
                        <input type="checkbox" class="w-4 h-4 text-blue-600 rounded focus:ring-blue-500">
                        <span class="text-gray-700">允许标签成员查看此任务</span>
                    </label>
                    <label class="flex items-center gap-3">
                        <input type="checkbox" class="w-4 h-4 text-blue-600 rounded focus:ring-blue-500" checked>
                        <span class="text-gray-700">创建后立即分享给执行人</span>
                    </label>
                    <label class="flex items-center gap-3">
                        <input type="checkbox" class="w-4 h-4 text-blue-600 rounded focus:ring-blue-500">
                        <span class="text-gray-700">状态变更时通知我</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- 创建按钮 -->
        <button class="create-btn">
            <i class="fas fa-check mr-2"></i>
            创建任务
        </button>

        <!-- 快速分享提示 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
            <div class="flex items-start gap-3">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-lightbulb text-blue-600 text-sm"></i>
                </div>
                <div>
                    <h4 class="font-semibold text-blue-900 mb-1">💡 小贴士</h4>
                    <p class="text-sm text-blue-700">
                        创建任务后，您可以通过"分享即加入"功能快速邀请微信好友参与协作，
                        对方点击分享卡片即可自动加入对应的标签群组。
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 优先级按钮切换
        document.querySelectorAll('.priority-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.priority-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });

        // 标签选择
        document.querySelectorAll('.tag-option').forEach(tag => {
            tag.addEventListener('click', () => {
                tag.classList.toggle('selected');
            });
        });

        // 成员选择
        document.querySelectorAll('.member-option').forEach(member => {
            member.addEventListener('click', () => {
                if (!member.querySelector('.fa-plus')) {
                    member.classList.toggle('selected');
                }
            });
        });

        // 快速操作
        document.querySelectorAll('.quick-action').forEach(action => {
            action.addEventListener('click', () => {
                document.querySelectorAll('.quick-action').forEach(a => a.classList.remove('active'));
                action.classList.add('active');
            });
        });

        // 表单验证
        function validateForm() {
            const title = document.querySelector('input[type="text"]').value.trim();
            const createBtn = document.querySelector('.create-btn');
            
            if (title.length > 0) {
                createBtn.disabled = false;
            } else {
                createBtn.disabled = true;
            }
        }

        document.querySelector('input[type="text"]').addEventListener('input', validateForm);
        validateForm(); // 初始验证
    </script>
</body>
</html>
