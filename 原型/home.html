<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>事绪录 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        
        .nav-bar {
            height: 60px;
            background: #ffffff;
            border-bottom: 1px solid #e5e5e5;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            position: relative;
        }
        
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: #ffffff;
            border-top: 1px solid #e5e5e5;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding-bottom: 20px;
            z-index: 100;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            transition: all 0.2s;
            color: #8E8E93;
        }
        
        .tab-item.active {
            color: #007AFF;
        }
        
        .tab-item i {
            font-size: 20px;
        }
        
        .tab-item span {
            font-size: 10px;
            font-weight: 500;
        }
        
        .content-area {
            padding-bottom: 100px;
            background: #F2F2F7;
            min-height: calc(100vh - 104px);
        }
        
        .task-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin: 8px 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            border-left: 4px solid #007AFF;
            transition: all 0.2s;
        }
        
        .task-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        
        .tag-pill {
            display: inline-block;
            background: #E3F2FD;
            color: #1976D2;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            margin-right: 8px;
        }
        
        .tag-pill.orange {
            background: #FFF3E0;
            color: #F57C00;
        }
        
        .tag-pill.green {
            background: #E8F5E8;
            color: #2E7D32;
        }
        
        .tag-pill.purple {
            background: #F3E5F5;
            color: #7B1FA2;
        }
        
        .fab {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #007AFF;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0,122,255,0.4);
            cursor: pointer;
            transition: all 0.2s;
            z-index: 99;
        }
        
        .fab:hover {
            transform: scale(1.1);
        }
        
        .priority-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .priority-high { background: #FF3B30; }
        .priority-medium { background: #FF9500; }
        .priority-low { background: #34C759; }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .status-pending {
            background: #FFF3E0;
            color: #F57C00;
        }
        
        .status-progress {
            background: #E3F2FD;
            color: #1976D2;
        }
        
        .status-completed {
            background: #E8F5E8;
            color: #2E7D32;
        }
        
        .tag-scroll {
            overflow-x: auto;
            white-space: nowrap;
            padding: 16px;
            background: white;
            border-bottom: 1px solid #e5e5e5;
        }
        
        .tag-scroll::-webkit-scrollbar {
            display: none;
        }
        
        .tag-filter {
            display: inline-block;
            padding: 8px 16px;
            margin-right: 12px;
            background: #F2F2F7;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            color: #1D1D1F;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
        }
        
        .tag-filter.active {
            background: #007AFF;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <span>9:41</span>
        <div class="flex items-center gap-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="flex items-center gap-3">
            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face&auto=format" 
                 class="w-8 h-8 rounded-full" alt="用户头像">
            <div>
                <h1 class="text-lg font-bold text-gray-900">事绪录</h1>
                <p class="text-xs text-gray-500">言语成绪，录而事成</p>
            </div>
        </div>
        <div class="flex items-center gap-4">
            <i class="fas fa-search text-gray-600 text-lg cursor-pointer"></i>
            <i class="fas fa-bell text-gray-600 text-lg cursor-pointer"></i>
        </div>
    </div>

    <!-- 标签筛选 -->
    <div class="tag-scroll">
        <button class="tag-filter active">全部</button>
        <button class="tag-filter"># 项目Alpha</button>
        <button class="tag-filter"># 客户Beta</button>
        <button class="tag-filter"># 个人事务</button>
        <button class="tag-filter"># 紧急处理</button>
        <button class="tag-filter"># 设计评审</button>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
        <!-- 今日统计 -->
        <div class="bg-white m-4 p-4 rounded-xl shadow-sm">
            <h3 class="text-sm font-semibold text-gray-700 mb-3">今日概览</h3>
            <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-blue-600">8</div>
                    <div class="text-xs text-gray-500">待处理</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-orange-600">3</div>
                    <div class="text-xs text-gray-500">进行中</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-green-600">12</div>
                    <div class="text-xs text-gray-500">已完成</div>
                </div>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="px-4">
            <div class="flex items-center justify-between mb-3">
                <h3 class="text-lg font-semibold text-gray-900">我的任务</h3>
                <span class="text-sm text-gray-500">按截止日期排序</span>
            </div>
        </div>

        <!-- 任务卡片1 -->
        <div class="task-card" style="border-left-color: #FF3B30;">
            <div class="flex items-start justify-between mb-2">
                <div class="flex-1">
                    <div class="flex items-center mb-1">
                        <span class="priority-indicator priority-high"></span>
                        <h4 class="font-semibold text-gray-900">完成品牌Logo最终设计稿</h4>
                    </div>
                    <p class="text-sm text-gray-600 mb-2">需要根据客户反馈调整配色方案，并准备3个备选方案</p>
                </div>
                <span class="status-badge status-pending">待处理</span>
            </div>
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <span class="tag-pill orange"># 项目Alpha</span>
                    <div class="flex items-center gap-1 text-xs text-gray-500">
                        <i class="fas fa-user"></i>
                        <span>张设计师</span>
                    </div>
                </div>
                <div class="flex items-center gap-2 text-xs text-gray-500">
                    <i class="fas fa-clock"></i>
                    <span>今天 18:00</span>
                </div>
            </div>
        </div>

        <!-- 任务卡片2 -->
        <div class="task-card" style="border-left-color: #FF9500;">
            <div class="flex items-start justify-between mb-2">
                <div class="flex-1">
                    <div class="flex items-center mb-1">
                        <span class="priority-indicator priority-medium"></span>
                        <h4 class="font-semibold text-gray-900">用户调研报告整理</h4>
                    </div>
                    <p class="text-sm text-gray-600 mb-2">汇总本周用户访谈结果，制作可视化图表</p>
                </div>
                <span class="status-badge status-progress">进行中</span>
            </div>
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <span class="tag-pill"># 客户Beta</span>
                    <div class="flex items-center gap-1 text-xs text-gray-500">
                        <i class="fas fa-user"></i>
                        <span>我</span>
                    </div>
                </div>
                <div class="flex items-center gap-2 text-xs text-gray-500">
                    <i class="fas fa-clock"></i>
                    <span>明天 12:00</span>
                </div>
            </div>
        </div>

        <!-- 任务卡片3 -->
        <div class="task-card" style="border-left-color: #34C759;">
            <div class="flex items-start justify-between mb-2">
                <div class="flex-1">
                    <div class="flex items-center mb-1">
                        <span class="priority-indicator priority-low"></span>
                        <h4 class="font-semibold text-gray-900">会议室预订</h4>
                    </div>
                    <p class="text-sm text-gray-600 mb-2">为下周的客户演示预订大会议室</p>
                </div>
                <span class="status-badge status-completed">已完成</span>
            </div>
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <span class="tag-pill green"># 个人事务</span>
                    <div class="flex items-center gap-1 text-xs text-gray-500">
                        <i class="fas fa-user"></i>
                        <span>我</span>
                    </div>
                </div>
                <div class="flex items-center gap-2 text-xs text-gray-500">
                    <i class="fas fa-check-circle text-green-500"></i>
                    <span>已完成</span>
                </div>
            </div>
        </div>

        <!-- 任务卡片4 -->
        <div class="task-card" style="border-left-color: #007AFF;">
            <div class="flex items-start justify-between mb-2">
                <div class="flex-1">
                    <div class="flex items-center mb-1">
                        <span class="priority-indicator priority-medium"></span>
                        <h4 class="font-semibold text-gray-900">API文档更新</h4>
                    </div>
                    <p class="text-sm text-gray-600 mb-2">更新v2.0版本的接口文档，添加新增功能说明</p>
                </div>
                <span class="status-badge status-pending">待处理</span>
            </div>
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <span class="tag-pill purple"># 设计评审</span>
                    <div class="flex items-center gap-1 text-xs text-gray-500">
                        <i class="fas fa-user"></i>
                        <span>李工程师</span>
                    </div>
                </div>
                <div class="flex items-center gap-2 text-xs text-gray-500">
                    <i class="fas fa-clock"></i>
                    <span>周五 15:00</span>
                </div>
            </div>
        </div>

        <!-- 协作邀请卡片 -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl m-4 p-4">
            <div class="flex items-center gap-3 mb-2">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-share-alt text-blue-600 text-sm"></i>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900">邀请团队成员</h4>
                    <p class="text-sm text-gray-600">通过分享任务快速邀请协作者</p>
                </div>
            </div>
            <button class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                了解"分享即加入"功能
            </button>
        </div>
    </div>

    <!-- 浮动按钮 -->
    <div class="fab">
        <i class="fas fa-plus"></i>
    </div>

    <!-- 底部 Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-tags"></i>
            <span>标签</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-chart-bar"></i>
            <span>统计</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
