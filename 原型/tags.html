<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签管理 - 事绪录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        
        .nav-bar {
            height: 60px;
            background: #ffffff;
            border-bottom: 1px solid #e5e5e5;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: #ffffff;
            border-top: 1px solid #e5e5e5;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding-bottom: 20px;
            z-index: 100;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            transition: all 0.2s;
            color: #8E8E93;
        }
        
        .tab-item.active {
            color: #007AFF;
        }
        
        .tab-item i {
            font-size: 20px;
        }
        
        .tab-item span {
            font-size: 10px;
            font-weight: 500;
        }
        
        .content-area {
            background: #F2F2F7;
            min-height: calc(100vh - 104px);
            padding-bottom: 100px;
        }
        
        .tag-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin: 8px 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.2s;
            position: relative;
        }
        
        .tag-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        
        .tag-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }
        
        .tag-name {
            font-size: 18px;
            font-weight: bold;
            color: #1D1D1F;
        }
        
        .tag-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .role-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .role-owner {
            background: #FFE4E1;
            color: #DC143C;
        }
        
        .role-admin {
            background: #E3F2FD;
            color: #1976D2;
        }
        
        .role-member {
            background: #F3E5F5;
            color: #7B1FA2;
        }
        
        .tag-stats {
            display: flex;
            gap: 16px;
            margin-bottom: 12px;
            font-size: 14px;
            color: #6B7280;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .member-avatars {
            display: flex;
            margin-bottom: 12px;
        }
        
        .member-avatar {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            border: 2px solid white;
            margin-left: -8px;
            object-fit: cover;
        }
        
        .member-avatar:first-child {
            margin-left: 0;
        }
        
        .more-members {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: #F2F2F7;
            border: 2px solid white;
            margin-left: -8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: #6B7280;
            font-weight: 600;
        }
        
        .tag-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
        }
        
        .action-btn.primary {
            background: #007AFF;
            color: white;
        }
        
        .action-btn.primary:hover {
            background: #0056CC;
        }
        
        .action-btn.secondary {
            background: #F2F2F7;
            color: #007AFF;
        }
        
        .action-btn.secondary:hover {
            background: #E5E5EA;
        }
        
        .create-tag-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 16px;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .create-tag-card:hover {
            transform: scale(1.02);
        }
        
        .search-bar {
            background: white;
            margin: 16px;
            border-radius: 12px;
            padding: 12px 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .search-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
            background: transparent;
        }
        
        .filter-tabs {
            display: flex;
            background: white;
            margin: 16px;
            margin-bottom: 8px;
            border-radius: 12px;
            padding: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .filter-tab {
            flex: 1;
            padding: 8px 16px;
            text-align: center;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .filter-tab.active {
            background: #007AFF;
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6B7280;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #F2F2F7;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 8px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007AFF, #5AC8FA);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <span>9:41</span>
        <div class="flex items-center gap-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <h1 class="text-lg font-semibold text-gray-900">标签管理</h1>
        <button class="text-blue-600">
            <i class="fas fa-plus text-lg"></i>
        </button>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
        <!-- 搜索栏 -->
        <div class="search-bar">
            <i class="fas fa-search text-gray-400"></i>
            <input type="text" class="search-input" placeholder="搜索标签或成员...">
            <i class="fas fa-filter text-gray-400 cursor-pointer"></i>
        </div>

        <!-- 筛选标签 -->
        <div class="filter-tabs">
            <div class="filter-tab active">全部标签</div>
            <div class="filter-tab">我管理的</div>
            <div class="filter-tab">我参与的</div>
        </div>

        <!-- 创建新标签卡片 -->
        <div class="create-tag-card">
            <i class="fas fa-plus text-2xl mb-2"></i>
            <h3 class="font-semibold text-lg mb-1">创建新标签</h3>
            <p class="text-sm opacity-90">开始一个新的协作空间</p>
        </div>

        <!-- 标签列表 -->
        
        <!-- 标签卡片1 - 项目Alpha -->
        <div class="tag-card">
            <div class="tag-header">
                <div class="flex items-center">
                    <div class="tag-color" style="background: #FF6B6B;"></div>
                    <span class="tag-name"># 项目Alpha</span>
                </div>
                <span class="role-badge role-owner">所有者</span>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" style="width: 75%;"></div>
            </div>
            
            <div class="tag-stats">
                <div class="stat-item">
                    <i class="fas fa-tasks"></i>
                    <span>8/12 任务</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-users"></i>
                    <span>5 成员</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-clock"></i>
                    <span>3天前</span>
                </div>
            </div>

            <div class="member-avatars">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=28&h=28&fit=crop&crop=face&auto=format" 
                     class="member-avatar" alt="安娜">
                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b2a1?w=28&h=28&fit=crop&crop=face&auto=format" 
                     class="member-avatar" alt="张设计师">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=28&h=28&fit=crop&crop=face&auto=format" 
                     class="member-avatar" alt="李工程师">
                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=28&h=28&fit=crop&crop=face&auto=format" 
                     class="member-avatar" alt="王产品">
                <div class="more-members">+1</div>
            </div>

            <div class="tag-actions">
                <button class="action-btn primary">
                    <i class="fas fa-cog mr-1"></i>
                    管理
                </button>
                <button class="action-btn secondary">
                    <i class="fas fa-share-alt mr-1"></i>
                    邀请
                </button>
            </div>
        </div>

        <!-- 标签卡片2 - 客户Beta -->
        <div class="tag-card">
            <div class="tag-header">
                <div class="flex items-center">
                    <div class="tag-color" style="background: #4ECDC4;"></div>
                    <span class="tag-name"># 客户Beta</span>
                </div>
                <span class="role-badge role-admin">管理员</span>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" style="width: 45%;"></div>
            </div>
            
            <div class="tag-stats">
                <div class="stat-item">
                    <i class="fas fa-tasks"></i>
                    <span>3/8 任务</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-users"></i>
                    <span>3 成员</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-clock"></i>
                    <span>1天前</span>
                </div>
            </div>

            <div class="member-avatars">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=28&h=28&fit=crop&crop=face&auto=format" 
                     class="member-avatar" alt="安娜">
                <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=28&h=28&fit=crop&crop=face&auto=format" 
                     class="member-avatar" alt="客户代表">
                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b2a1?w=28&h=28&fit=crop&crop=face&auto=format" 
                     class="member-avatar" alt="张设计师">
            </div>

            <div class="tag-actions">
                <button class="action-btn primary">
                    <i class="fas fa-cog mr-1"></i>
                    管理
                </button>
                <button class="action-btn secondary">
                    <i class="fas fa-share-alt mr-1"></i>
                    邀请
                </button>
            </div>
        </div>

        <!-- 标签卡片3 - 个人事务 -->
        <div class="tag-card">
            <div class="tag-header">
                <div class="flex items-center">
                    <div class="tag-color" style="background: #95E1D3;"></div>
                    <span class="tag-name"># 个人事务</span>
                </div>
                <span class="role-badge role-owner">所有者</span>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" style="width: 90%;"></div>
            </div>
            
            <div class="tag-stats">
                <div class="stat-item">
                    <i class="fas fa-tasks"></i>
                    <span>9/10 任务</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-users"></i>
                    <span>1 成员</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-clock"></i>
                    <span>今天</span>
                </div>
            </div>

            <div class="member-avatars">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=28&h=28&fit=crop&crop=face&auto=format" 
                     class="member-avatar" alt="我">
            </div>

            <div class="tag-actions">
                <button class="action-btn primary">
                    <i class="fas fa-cog mr-1"></i>
                    管理
                </button>
                <button class="action-btn secondary">
                    <i class="fas fa-share-alt mr-1"></i>
                    邀请
                </button>
            </div>
        </div>

        <!-- 标签卡片4 - 紧急处理 -->
        <div class="tag-card">
            <div class="tag-header">
                <div class="flex items-center">
                    <div class="tag-color" style="background: #FF8A80;"></div>
                    <span class="tag-name"># 紧急处理</span>
                </div>
                <span class="role-badge role-member">成员</span>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" style="width: 20%;"></div>
            </div>
            
            <div class="tag-stats">
                <div class="stat-item">
                    <i class="fas fa-tasks"></i>
                    <span>1/5 任务</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-users"></i>
                    <span>4 成员</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-clock"></i>
                    <span>2小时前</span>
                </div>
            </div>

            <div class="member-avatars">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=28&h=28&fit=crop&crop=face&auto=format" 
                     class="member-avatar" alt="李工程师">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=28&h=28&fit=crop&crop=face&auto=format" 
                     class="member-avatar" alt="安娜">
                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=28&h=28&fit=crop&crop=face&auto=format" 
                     class="member-avatar" alt="王产品">
                <div class="more-members">+1</div>
            </div>

            <div class="tag-actions">
                <button class="action-btn secondary">
                    <i class="fas fa-eye mr-1"></i>
                    查看
                </button>
                <button class="action-btn secondary">
                    <i class="fas fa-comment mr-1"></i>
                    讨论
                </button>
            </div>
        </div>

        <!-- 协作提示 -->
        <div class="bg-blue-50 border border-blue-200 rounded-xl m-4 p-4">
            <div class="flex items-center gap-3 mb-2">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-info text-blue-600 text-sm"></i>
                </div>
                <div>
                    <h4 class="font-semibold text-blue-900">标签即群组</h4>
                    <p class="text-sm text-blue-700">每个标签就是一个独立的协作空间，成员可以共享该标签下的所有任务</p>
                </div>
            </div>
        </div>

        <!-- 使用统计 -->
        <div class="bg-white m-4 p-4 rounded-xl shadow-sm">
            <h3 class="font-semibold text-gray-900 mb-3">本周统计</h3>
            <div class="grid grid-cols-2 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">23</div>
                    <div class="text-xs text-gray-500">完成任务</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">4</div>
                    <div class="text-xs text-gray-500">活跃标签</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部 Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item active">
            <i class="fas fa-tags"></i>
            <span>标签</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-chart-bar"></i>
            <span>统计</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>

    <script>
        // 筛选标签切换
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
            });
        });

        // 搜索功能
        document.querySelector('.search-input').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const tagCards = document.querySelectorAll('.tag-card');
            
            tagCards.forEach(card => {
                const tagName = card.querySelector('.tag-name').textContent.toLowerCase();
                if (tagName.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });

        // 操作按钮点击事件
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const action = this.textContent.trim();
                console.log(`执行操作: ${action}`);
            });
        });
    </script>
</body>
</html>
