<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 事绪录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        
        .nav-bar {
            height: 60px;
            background: #ffffff;
            border-bottom: 1px solid #e5e5e5;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: #ffffff;
            border-top: 1px solid #e5e5e5;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding-bottom: 20px;
            z-index: 100;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            transition: all 0.2s;
            color: #8E8E93;
        }
        
        .tab-item.active {
            color: #007AFF;
        }
        
        .tab-item i {
            font-size: 20px;
        }
        
        .tab-item span {
            font-size: 10px;
            font-weight: 500;
        }
        
        .content-area {
            background: #F2F2F7;
            min-height: calc(100vh - 104px);
            padding-bottom: 100px;
        }
        
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px 20px;
            text-align: center;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 4px solid white;
            margin: 0 auto 12px;
            object-fit: cover;
        }
        
        .profile-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .profile-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .stats-card {
            background: white;
            margin: 16px;
            margin-top: -20px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            text-align: center;
        }
        
        .stat-item {
            padding: 12px;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007AFF;
        }
        
        .stat-label {
            font-size: 12px;
            color: #6B7280;
            margin-top: 4px;
        }
        
        .menu-card {
            background: white;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #F2F2F7;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .menu-item:last-child {
            border-bottom: none;
        }
        
        .menu-item:hover {
            background: #F8F9FA;
        }
        
        .menu-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
        }
        
        .menu-content {
            flex: 1;
        }
        
        .menu-title {
            font-weight: 500;
            color: #1D1D1F;
            margin-bottom: 2px;
        }
        
        .menu-subtitle {
            font-size: 12px;
            color: #6B7280;
        }
        
        .menu-arrow {
            color: #C7C7CC;
        }
        
        .achievement-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 16px;
            border-radius: 12px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .achievement-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></svg>') no-repeat;
            background-size: 120px 120px;
        }
        
        .version-info {
            text-align: center;
            padding: 20px;
            color: #6B7280;
            font-size: 12px;
        }
        
        .switch-toggle {
            position: relative;
            width: 48px;
            height: 28px;
            background: #E5E5EA;
            border-radius: 14px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .switch-toggle.active {
            background: #007AFF;
        }
        
        .switch-toggle::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .switch-toggle.active::after {
            transform: translateX(20px);
        }
        
        .badge {
            background: #FF3B30;
            color: white;
            font-size: 10px;
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: auto;
        }
        
        .recent-activity {
            margin: 16px;
        }
        
        .activity-item {
            background: white;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        
        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-size: 14px;
            color: #1D1D1F;
            margin-bottom: 2px;
        }
        
        .activity-time {
            font-size: 11px;
            color: #6B7280;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <span>9:41</span>
        <div class="flex items-center gap-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <h1 class="text-lg font-semibold text-gray-900">个人中心</h1>
        <button class="text-blue-600">
            <i class="fas fa-cog text-lg"></i>
        </button>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
        <!-- 个人信息头部 -->
        <div class="profile-header">
            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face&auto=format" 
                 class="profile-avatar" alt="安娜">
            <div class="profile-name">安娜</div>
            <div class="profile-subtitle">广告公司项目经理 · 使用事绪录 92 天</div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-card">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">127</div>
                    <div class="stat-label">完成任务</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">8</div>
                    <div class="stat-label">管理标签</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">23</div>
                    <div class="stat-label">协作成员</div>
                </div>
            </div>
        </div>

        <!-- 成就卡片 -->
        <div class="achievement-card">
            <div class="flex items-center justify-between relative z-10">
                <div>
                    <h3 class="text-lg font-bold mb-1">效率达人</h3>
                    <p class="text-sm opacity-90">连续7天完成所有任务</p>
                </div>
                <div class="text-3xl">🏆</div>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="recent-activity">
            <h3 class="text-lg font-semibold text-gray-900 mb-3 px-4">最近活动</h3>
            
            <div class="activity-item">
                <div class="activity-icon bg-green-100 text-green-600">
                    <i class="fas fa-check"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">完成任务"品牌Logo设计"</div>
                    <div class="activity-time">2小时前</div>
                </div>
            </div>
            
            <div class="activity-item">
                <div class="activity-icon bg-blue-100 text-blue-600">
                    <i class="fas fa-share-alt"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">邀请张设计师加入 # 项目Alpha</div>
                    <div class="activity-time">5小时前</div>
                </div>
            </div>
            
            <div class="activity-item">
                <div class="activity-icon bg-purple-100 text-purple-600">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">创建新标签 # 客户Beta</div>
                    <div class="activity-time">昨天</div>
                </div>
            </div>
        </div>

        <!-- 功能菜单 -->
        <div class="menu-card">
            <div class="menu-item">
                <div class="menu-icon bg-blue-100 text-blue-600">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">消息通知</div>
                    <div class="menu-subtitle">管理通知设置和提醒</div>
                </div>
                <div class="switch-toggle active"></div>
            </div>
            
            <div class="menu-item">
                <div class="menu-icon bg-green-100 text-green-600">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">隐私设置</div>
                    <div class="menu-subtitle">个人信息和数据保护</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
            
            <div class="menu-item">
                <div class="menu-icon bg-orange-100 text-orange-600">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">使用统计</div>
                    <div class="menu-subtitle">查看详细的效率报告</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
            
            <div class="menu-item">
                <div class="menu-icon bg-purple-100 text-purple-600">
                    <i class="fas fa-download"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">数据导出</div>
                    <div class="menu-subtitle">导出任务和协作数据</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
        </div>

        <!-- 账户菜单 -->
        <div class="menu-card">
            <div class="menu-item">
                <div class="menu-icon bg-gray-100 text-gray-600">
                    <i class="fas fa-user-edit"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">编辑资料</div>
                    <div class="menu-subtitle">修改个人信息</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
            
            <div class="menu-item">
                <div class="menu-icon bg-yellow-100 text-yellow-600">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">升级专业版</div>
                    <div class="menu-subtitle">解锁更多高级功能</div>
                </div>
                <div class="badge">NEW</div>
            </div>
            
            <div class="menu-item">
                <div class="menu-icon bg-teal-100 text-teal-600">
                    <i class="fas fa-gift"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">邀请好友</div>
                    <div class="menu-subtitle">分享事绪录给更多人</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
        </div>

        <!-- 帮助菜单 -->
        <div class="menu-card">
            <div class="menu-item">
                <div class="menu-icon bg-indigo-100 text-indigo-600">
                    <i class="fas fa-question-circle"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">帮助中心</div>
                    <div class="menu-subtitle">使用指南和常见问题</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
            
            <div class="menu-item">
                <div class="menu-icon bg-pink-100 text-pink-600">
                    <i class="fas fa-comment-alt"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">意见反馈</div>
                    <div class="menu-subtitle">告诉我们您的想法</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
            
            <div class="menu-item">
                <div class="menu-icon bg-red-100 text-red-600">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">关于事绪录</div>
                    <div class="menu-subtitle">版本信息和法律条款</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
        </div>

        <!-- 版本信息 -->
        <div class="version-info">
            <p>事绪录 v1.0.0</p>
            <p>言语成绪，录而事成</p>
        </div>
    </div>

    <!-- 底部 Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-tags"></i>
            <span>标签</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-chart-bar"></i>
            <span>统计</span>
        </div>
        <div class="tab-item active">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>

    <script>
        // 开关切换
        document.querySelectorAll('.switch-toggle').forEach(toggle => {
            toggle.addEventListener('click', () => {
                toggle.classList.toggle('active');
            });
        });

        // 菜单项点击事件
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function() {
                const title = this.querySelector('.menu-title').textContent;
                console.log(`点击菜单: ${title}`);
                
                // 如果有开关，阻止事件冒泡
                if (this.querySelector('.switch-toggle')) {
                    return;
                }
                
                // 这里可以添加页面跳转或弹窗逻辑
                if (title === '升级专业版') {
                    alert('升级专业版功能即将上线！');
                } else if (title === '意见反馈') {
                    alert('感谢您的反馈！请在微信群或邮件中联系我们。');
                } else if (title === '关于事绪录') {
                    alert('事绪录 v1.0.0\n言语成绪，录而事成\n\n让一切井井有条的掌控感和安全感');
                }
            });
        });

        // 统计数字动画
        function animateNumbers() {
            const statNumbers = document.querySelectorAll('.stat-number');
            const targets = [127, 8, 23];
            
            statNumbers.forEach((num, index) => {
                let current = 0;
                const target = targets[index];
                const increment = target / 30;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    num.textContent = Math.floor(current);
                }, 50);
            });
        }

        // 页面加载完成后执行动画
        window.addEventListener('load', animateNumbers);
    </script>
</body>
</html>
