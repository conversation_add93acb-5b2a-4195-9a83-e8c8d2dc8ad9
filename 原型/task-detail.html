<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务详情 - 事绪录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        
        .nav-bar {
            height: 60px;
            background: #ffffff;
            border-bottom: 1px solid #e5e5e5;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            position: relative;
        }
        
        .content-area {
            background: #F2F2F7;
            min-height: calc(100vh - 104px);
            padding-bottom: 20px;
        }
        
        .detail-card {
            background: white;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .priority-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .priority-high { background: #FF3B30; }
        .priority-medium { background: #FF9500; }
        .priority-low { background: #34C759; }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .status-pending {
            background: #FFF3E0;
            color: #F57C00;
        }
        
        .status-progress {
            background: #E3F2FD;
            color: #1976D2;
        }
        
        .status-completed {
            background: #E8F5E8;
            color: #2E7D32;
        }
        
        .tag-pill {
            display: inline-block;
            background: #E3F2FD;
            color: #1976D2;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            margin-right: 8px;
        }
        
        .tag-pill.orange {
            background: #FFF3E0;
            color: #F57C00;
        }
        
        .action-button {
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .action-button:hover {
            background: #0056CC;
        }
        
        .action-button.secondary {
            background: #F2F2F7;
            color: #007AFF;
        }
        
        .action-button.secondary:hover {
            background: #E5E5EA;
        }
        
        .comment-card {
            background: #F8F9FA;
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 12px;
            border-left: 3px solid #007AFF;
        }
        
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .share-modal {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-radius: 16px 16px 0 0;
            padding: 24px;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }
        
        .share-modal.show {
            transform: translateY(0);
        }
        
        .share-option {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .share-option:hover {
            background: #F2F2F7;
        }
        
        .progress-ring {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: conic-gradient(#007AFF 0deg 216deg, #E5E5EA 216deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .progress-ring::before {
            content: '';
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: white;
            position: absolute;
        }
        
        .progress-text {
            position: relative;
            z-index: 1;
            font-size: 10px;
            font-weight: bold;
            color: #007AFF;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <span>9:41</span>
        <div class="flex items-center gap-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <button class="flex items-center gap-2 text-blue-600">
            <i class="fas fa-chevron-left"></i>
            <span>返回</span>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">任务详情</h1>
        <button class="text-blue-600">
            <i class="fas fa-ellipsis-h text-lg"></i>
        </button>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
        <!-- 任务基本信息 -->
        <div class="detail-card">
            <div class="p-6">
                <!-- 标题和状态 -->
                <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <span class="priority-indicator priority-high"></span>
                            <h2 class="text-xl font-bold text-gray-900">完成品牌Logo最终设计稿</h2>
                        </div>
                        <span class="status-badge status-pending">待处理</span>
                    </div>
                    <div class="progress-ring">
                        <span class="progress-text">60%</span>
                    </div>
                </div>

                <!-- 标签 -->
                <div class="mb-4">
                    <span class="tag-pill orange"># 项目Alpha</span>
                </div>

                <!-- 描述 -->
                <div class="mb-6">
                    <h3 class="font-semibold text-gray-700 mb-2">任务描述</h3>
                    <p class="text-gray-600 leading-relaxed">
                        需要根据客户反馈调整配色方案，并准备3个备选方案。要求保持品牌调性的一致性，
                        同时确保在不同尺寸下的清晰度。请在完成后提供AI和PNG两种格式的文件。
                    </p>
                </div>

                <!-- 任务信息 -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <div>
                        <h4 class="text-sm font-semibold text-gray-700 mb-1">执行人</h4>
                        <div class="flex items-center gap-2">
                            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face&auto=format" 
                                 class="avatar" alt="张设计师">
                            <span class="text-gray-900">张设计师</span>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-700 mb-1">截止时间</h4>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-clock text-red-500"></i>
                            <span class="text-red-600 font-medium">今天 18:00</span>
                        </div>
                    </div>
                </div>

                <!-- 创建信息 -->
                <div class="border-t pt-4">
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <span>创建于 2024年1月15日 09:30</span>
                        <span>创建者：安娜</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="px-4 mb-6">
            <div class="grid grid-cols-2 gap-3">
                <button class="action-button">
                    <i class="fas fa-share-alt"></i>
                    分享任务
                </button>
                <button class="action-button secondary">
                    <i class="fas fa-edit"></i>
                    编辑任务
                </button>
            </div>
        </div>

        <!-- 状态更新 -->
        <div class="detail-card">
            <div class="p-6">
                <h3 class="font-semibold text-gray-900 mb-4">更新状态</h3>
                <div class="space-y-3">
                    <button class="w-full flex items-center justify-between p-3 border border-orange-200 bg-orange-50 rounded-lg">
                        <div class="flex items-center gap-3">
                            <div class="w-4 h-4 rounded-full bg-orange-500"></div>
                            <span class="font-medium text-orange-700">待处理</span>
                        </div>
                        <i class="fas fa-check text-orange-500"></i>
                    </button>
                    <button class="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                        <div class="flex items-center gap-3">
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                            <span class="font-medium text-gray-700">进行中</span>
                        </div>
                    </button>
                    <button class="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                        <div class="flex items-center gap-3">
                            <div class="w-4 h-4 rounded-full bg-green-500"></div>
                            <span class="font-medium text-gray-700">已完成</span>
                        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- 讨论区 -->
        <div class="detail-card">
            <div class="p-6">
                <h3 class="font-semibold text-gray-900 mb-4">讨论 (3)</h3>
                
                <!-- 评论1 -->
                <div class="comment-card">
                    <div class="flex items-start gap-3 mb-2">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face&auto=format" 
                             class="avatar" alt="安娜">
                        <div class="flex-1">
                            <div class="flex items-center gap-2 mb-1">
                                <span class="font-semibold text-gray-900">安娜</span>
                                <span class="text-xs text-gray-500">2小时前</span>
                            </div>
                            <p class="text-gray-700">客户希望Logo能更突出科技感，建议尝试渐变色彩，并适当增加几何元素。</p>
                        </div>
                    </div>
                </div>

                <!-- 评论2 -->
                <div class="comment-card">
                    <div class="flex items-start gap-3 mb-2">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face&auto=format" 
                             class="avatar" alt="张设计师">
                        <div class="flex-1">
                            <div class="flex items-center gap-2 mb-1">
                                <span class="font-semibold text-gray-900">张设计师</span>
                                <span class="text-xs text-gray-500">1小时前</span>
                            </div>
                            <p class="text-gray-700">了解，我会准备几个渐变方案。请问对色彩有什么特别偏好吗？</p>
                        </div>
                    </div>
                </div>

                <!-- 评论3 -->
                <div class="comment-card">
                    <div class="flex items-start gap-3 mb-2">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face&auto=format" 
                             class="avatar" alt="安娜">
                        <div class="flex-1">
                            <div class="flex items-center gap-2 mb-1">
                                <span class="font-semibold text-gray-900">安娜</span>
                                <span class="text-xs text-gray-500">30分钟前</span>
                            </div>
                            <p class="text-gray-700">蓝色到紫色的渐变会比较符合品牌调性，避免使用过于鲜艳的颜色。</p>
                        </div>
                    </div>
                </div>

                <!-- 添加评论 -->
                <div class="mt-4 pt-4 border-t">
                    <div class="flex items-center gap-3">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face&auto=format" 
                             class="avatar" alt="我">
                        <div class="flex-1 flex items-center gap-2">
                            <input type="text" placeholder="添加评论..." 
                                   class="flex-1 px-4 py-2 border border-gray-200 rounded-full focus:outline-none focus:border-blue-500">
                            <button class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center">
                                <i class="fas fa-paper-plane text-xs"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 相关任务 -->
        <div class="detail-card">
            <div class="p-6">
                <h3 class="font-semibold text-gray-900 mb-4">相关任务 (# 项目Alpha)</h3>
                <div class="space-y-3">
                    <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span class="flex-1 text-gray-700">品牌VI手册制作</span>
                        <span class="text-xs text-gray-500">已完成</span>
                    </div>
                    <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span class="flex-1 text-gray-700">网站Banner设计</span>
                        <span class="text-xs text-gray-500">进行中</span>
                    </div>
                    <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                        <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span class="flex-1 text-gray-700">宣传物料设计</span>
                        <span class="text-xs text-gray-500">待处理</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分享弹窗 (隐藏状态) -->
    <div class="share-modal" id="shareModal">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold">分享任务</h3>
            <button onclick="closeShareModal()" class="text-gray-500">
                <i class="fas fa-times text-lg"></i>
            </button>
        </div>
        
        <div class="space-y-2">
            <div class="share-option">
                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <i class="fab fa-weixin text-green-600"></i>
                </div>
                <div>
                    <div class="font-medium">微信好友</div>
                    <div class="text-sm text-gray-500">邀请好友加入标签群组</div>
                </div>
            </div>
            
            <div class="share-option">
                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-users text-green-600"></i>
                </div>
                <div>
                    <div class="font-medium">微信群聊</div>
                    <div class="text-sm text-gray-500">分享到群聊进行讨论</div>
                </div>
            </div>
            
            <div class="share-option">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-link text-blue-600"></i>
                </div>
                <div>
                    <div class="font-medium">复制链接</div>
                    <div class="text-sm text-gray-500">生成邀请链接</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openShareModal() {
            document.getElementById('shareModal').classList.add('show');
        }
        
        function closeShareModal() {
            document.getElementById('shareModal').classList.remove('show');
        }
        
        // 分享按钮点击事件
        document.querySelector('.action-button').addEventListener('click', openShareModal);
    </script>
</body>
</html>
