-- 事绪录小程序数据库表结构
-- 基于微信生态的轻量级协作任务管理系统
-- 创建时间: 2025-01-24

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 1. 用户表
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(64) NOT NULL COMMENT '微信openid，唯一标识',
  `nickname` varchar(100) DEFAULT NULL COMMENT '微信昵称',
  `avatar_url` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号（可选）',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱（可选）',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`),
  KEY `idx_nickname` (`nickname`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 标签表（标签即群组）
DROP TABLE IF EXISTS `tags`;
CREATE TABLE `tags` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(100) NOT NULL COMMENT '标签名称',
  `color` varchar(20) DEFAULT '#007AFF' COMMENT '标签颜色',
  `description` text COMMENT '标签描述',
  `creator_id` bigint(20) unsigned NOT NULL COMMENT '创建者ID',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `member_count` int(11) NOT NULL DEFAULT 1 COMMENT '成员数量（冗余字段）',
  `task_count` int(11) NOT NULL DEFAULT 0 COMMENT '任务数量（冗余字段）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_name` (`name`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_tags_creator` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表（群组）';

-- 3. 标签成员关系表
DROP TABLE IF EXISTS `tag_members`;
CREATE TABLE `tag_members` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `tag_id` bigint(20) unsigned NOT NULL COMMENT '标签ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `role` enum('owner','admin','member') NOT NULL DEFAULT 'member' COMMENT '角色：所有者/管理员/成员',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tag_user` (`tag_id`, `user_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role` (`role`),
  CONSTRAINT `fk_tag_members_tag` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_tag_members_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签成员关系表';

-- 4. 任务表
DROP TABLE IF EXISTS `tasks`;
CREATE TABLE `tasks` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `title` varchar(255) NOT NULL COMMENT '任务标题',
  `description` text COMMENT '任务描述',
  `tag_id` bigint(20) unsigned NOT NULL COMMENT '所属标签ID',
  `creator_id` bigint(20) unsigned NOT NULL COMMENT '创建者ID',
  `assignee_id` bigint(20) unsigned DEFAULT NULL COMMENT '执行人ID',
  `status` enum('pending','in_progress','completed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '任务状态',
  `priority` enum('low','medium','high') NOT NULL DEFAULT 'medium' COMMENT '优先级',
  `due_date` timestamp NULL DEFAULT NULL COMMENT '截止日期',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `is_archived` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否归档',
  `comment_count` int(11) NOT NULL DEFAULT 0 COMMENT '评论数量（冗余字段）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_tag_id` (`tag_id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_assignee_id` (`assignee_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_due_date` (`due_date`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status_due_date` (`status`, `due_date`),
  CONSTRAINT `fk_tasks_tag` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_tasks_creator` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_tasks_assignee` FOREIGN KEY (`assignee_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';

-- 5. 任务评论表
DROP TABLE IF EXISTS `task_comments`;
CREATE TABLE `task_comments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `task_id` bigint(20) unsigned NOT NULL COMMENT '任务ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '评论者ID',
  `content` text NOT NULL COMMENT '评论内容',
  `parent_id` bigint(20) unsigned DEFAULT NULL COMMENT '父评论ID（支持回复）',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_task_comments_task` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_task_comments_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_task_comments_parent` FOREIGN KEY (`parent_id`) REFERENCES `task_comments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务评论表';

-- 6. 分享令牌表（分享即加入功能）
DROP TABLE IF EXISTS `share_tokens`;
CREATE TABLE `share_tokens` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '令牌ID',
  `token` varchar(128) NOT NULL COMMENT '分享令牌',
  `task_id` bigint(20) unsigned NOT NULL COMMENT '关联任务ID',
  `tag_id` bigint(20) unsigned NOT NULL COMMENT '关联标签ID',
  `creator_id` bigint(20) unsigned NOT NULL COMMENT '创建分享的用户ID',
  `expires_at` timestamp NOT NULL COMMENT '过期时间',
  `used_at` timestamp NULL DEFAULT NULL COMMENT '使用时间',
  `used_by` bigint(20) unsigned DEFAULT NULL COMMENT '使用者ID',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否有效',
  `max_uses` int(11) NOT NULL DEFAULT 1 COMMENT '最大使用次数',
  `use_count` int(11) NOT NULL DEFAULT 0 COMMENT '已使用次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_token` (`token`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_tag_id` (`tag_id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_share_tokens_task` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_share_tokens_tag` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_share_tokens_creator` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_share_tokens_used_by` FOREIGN KEY (`used_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享令牌表';

-- 7. 通知记录表
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '接收通知的用户ID',
  `type` enum('task_assigned','task_updated','task_commented','task_due','task_completed','tag_invited') NOT NULL COMMENT '通知类型',
  `title` varchar(255) NOT NULL COMMENT '通知标题',
  `content` text COMMENT '通知内容',
  `related_task_id` bigint(20) unsigned DEFAULT NULL COMMENT '相关任务ID',
  `related_tag_id` bigint(20) unsigned DEFAULT NULL COMMENT '相关标签ID',
  `related_user_id` bigint(20) unsigned DEFAULT NULL COMMENT '相关用户ID（如操作者）',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读',
  `sent_at` timestamp NULL DEFAULT NULL COMMENT '发送时间',
  `wx_template_id` varchar(100) DEFAULT NULL COMMENT '微信模板消息ID',
  `wx_form_id` varchar(100) DEFAULT NULL COMMENT '微信表单ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_related_task_id` (`related_task_id`),
  KEY `idx_related_tag_id` (`related_tag_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_read_created` (`user_id`, `is_read`, `created_at`),
  CONSTRAINT `fk_notifications_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_notifications_task` FOREIGN KEY (`related_task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_notifications_tag` FOREIGN KEY (`related_tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_notifications_related_user` FOREIGN KEY (`related_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知记录表';

-- 8. 任务操作日志表（可选，用于审计）
DROP TABLE IF EXISTS `task_logs`;
CREATE TABLE `task_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `task_id` bigint(20) unsigned NOT NULL COMMENT '任务ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '操作用户ID',
  `action` enum('created','updated','assigned','completed','commented','deleted','archived') NOT NULL COMMENT '操作类型',
  `old_value` json DEFAULT NULL COMMENT '旧值（JSON格式）',
  `new_value` json DEFAULT NULL COMMENT '新值（JSON格式）',
  `description` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_task_logs_task` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_task_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务操作日志表';

-- 9. 系统配置表
DROP TABLE IF EXISTS `system_configs`;
CREATE TABLE `system_configs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 创建视图：用户任务统计
CREATE VIEW `user_task_stats` AS
SELECT
  u.id as user_id,
  u.nickname,
  COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_count,
  COUNT(CASE WHEN t.status = 'in_progress' THEN 1 END) as in_progress_count,
  COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_count,
  COUNT(t.id) as total_count,
  COUNT(CASE WHEN DATE(t.due_date) = CURDATE() THEN 1 END) as due_today_count,
  COUNT(CASE WHEN t.due_date < NOW() AND t.status != 'completed' THEN 1 END) as overdue_count
FROM users u
LEFT JOIN tasks t ON (u.id = t.assignee_id AND t.is_archived = 0)
GROUP BY u.id, u.nickname;

-- 创建视图：标签统计
CREATE VIEW `tag_stats` AS
SELECT
  tg.id as tag_id,
  tg.name as tag_name,
  tg.color,
  COUNT(DISTINCT tm.user_id) as member_count,
  COUNT(t.id) as task_count,
  COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_task_count,
  COUNT(CASE WHEN t.status = 'in_progress' THEN 1 END) as in_progress_task_count,
  COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_task_count,
  tg.created_at
FROM tags tg
LEFT JOIN tag_members tm ON (tg.id = tm.tag_id AND tm.is_active = 1)
LEFT JOIN tasks t ON (tg.id = t.tag_id AND t.is_archived = 0)
WHERE tg.is_active = 1
GROUP BY tg.id, tg.name, tg.color, tg.created_at;

-- 插入默认系统配置
INSERT INTO `system_configs` (`config_key`, `config_value`, `description`) VALUES
('app_name', '事绪录', '应用名称'),
('app_version', '1.0.0', '应用版本'),
('max_tag_members', '50', '每个标签最大成员数'),
('max_user_tags', '20', '每个用户最大标签数'),
('share_token_expire_hours', '24', '分享令牌过期时间（小时）'),
('notification_batch_size', '100', '通知批处理大小'),
('task_due_remind_hours', '2', '任务到期提醒提前时间（小时）');

SET FOREIGN_KEY_CHECKS = 1;
